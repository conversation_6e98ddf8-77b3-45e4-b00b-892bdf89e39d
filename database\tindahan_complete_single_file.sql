-- =====================================================
-- 🏪 TINDAHAN COMPLETE DATABASE - SINGLE FILE SOLUTION
-- =====================================================
-- ✅ PRODUCTION-SAFE: Hindi mawawala ang existing data
-- ✅ SINGLE FILE: Isang copy-paste lang sa Supabase SQL Editor
-- ✅ COMPLETE: Lahat ng kailangan nandito na
-- ✅ IDEMPOTENT: Safe i-run multiple times
-- 
-- 📋 INSTRUCTIONS:
-- 1. Copy ang BUONG file na ito (Ctrl+A, Ctrl+C)
-- 2. Paste sa Supabase SQL Editor (Ctrl+V)
-- 3. Run (Ctrl+Enter)
-- 4. ✅ TAPOS NA! Database ready na
-- 
-- 🎯 FEATURES:
-- - Products management
-- - Customers management  
-- - Debt tracking
-- - Payment tracking
-- - Real-time balance calculations
-- - Cloudinary image support
-- - Security policies
-- 
-- Version: 3.0 - Single File Production Safe
-- Date: 2025-01-28
-- =====================================================

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Set timezone
SET timezone = 'Asia/Manila';

-- =====================================================
-- 🚀 SETUP START MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🏪 =====================================================';
    RAISE NOTICE '🏪 TINDAHAN DATABASE SETUP STARTING...';
    RAISE NOTICE '🏪 =====================================================';
    RAISE NOTICE '✅ Production-safe setup - existing data preserved';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- 🛡️ SAFE CLEANUP (NO DATA LOSS)
-- =====================================================
DO $$ BEGIN RAISE NOTICE '🔧 Step 1/8: Cleaning up policies and triggers...'; END $$;

-- Drop policies (safe)
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON products;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customers;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customer_debts;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customer_payments;
DROP POLICY IF EXISTS "Enable all operations for application" ON products;
DROP POLICY IF EXISTS "Enable all operations for application" ON customers;
DROP POLICY IF EXISTS "Enable all operations for application" ON customer_debts;
DROP POLICY IF EXISTS "Enable all operations for application" ON customer_payments;

-- Drop views (safe)
DROP VIEW IF EXISTS customer_balances CASCADE;

-- Drop triggers (safe)
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
DROP TRIGGER IF EXISTS update_customer_debts_updated_at ON customer_debts;
DROP TRIGGER IF EXISTS update_customer_payments_updated_at ON customer_payments;
DROP TRIGGER IF EXISTS validate_customer_payment ON customer_payments;

-- Drop functions (safe)
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS validate_payment_amount() CASCADE;

-- =====================================================
-- 📊 TABLES CREATION (SAFE - NO DATA LOSS)
-- =====================================================
DO $$ BEGIN RAISE NOTICE '🔧 Step 2/8: Creating/updating tables...'; END $$;

-- PRODUCTS TABLE
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    image_url TEXT,
    net_weight VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    category VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT products_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT products_category_not_empty CHECK (LENGTH(TRIM(category)) > 0),
    CONSTRAINT products_net_weight_not_empty CHECK (LENGTH(TRIM(net_weight)) > 0)
);

-- CUSTOMERS TABLE
CREATE TABLE IF NOT EXISTS customers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    profile_picture_url TEXT,
    profile_picture_public_id TEXT,
    phone_number VARCHAR(20),
    address TEXT,
    birth_date DATE,
    birth_place VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT customers_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customers_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customers_birth_date_valid CHECK (birth_date IS NULL OR birth_date <= CURRENT_DATE),
    CONSTRAINT customers_birth_place_not_empty CHECK (birth_place IS NULL OR LENGTH(TRIM(birth_place)) > 0)
);

-- CUSTOMER DEBTS TABLE
CREATE TABLE IF NOT EXISTS customer_debts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL CHECK (product_price > 0),
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT customer_debts_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customer_debts_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customer_debts_product_name_not_empty CHECK (LENGTH(TRIM(product_name)) > 0),
    CONSTRAINT customer_debts_reasonable_quantity CHECK (quantity <= 1000),
    CONSTRAINT customer_debts_valid_date CHECK (debt_date >= '2020-01-01' AND debt_date <= CURRENT_DATE + INTERVAL '1 day')
);

-- CUSTOMER PAYMENTS TABLE
CREATE TABLE IF NOT EXISTS customer_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    payment_amount DECIMAL(10,2) NOT NULL CHECK (payment_amount > 0),
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'Cash',
    family_member_name VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT customer_payments_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customer_payments_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customer_payments_reasonable_amount CHECK (payment_amount <= 1000000),
    CONSTRAINT customer_payments_valid_date CHECK (payment_date >= '2020-01-01' AND payment_date <= CURRENT_DATE + INTERVAL '1 day'),
    CONSTRAINT customer_payments_valid_method CHECK (payment_method IN ('Cash', 'GCash', 'Bank Transfer', 'Credit', 'Other'))
);

-- =====================================================
-- 🔧 FUNCTIONS CREATION
-- =====================================================
DO $$ BEGIN RAISE NOTICE '🔧 Step 3/8: Creating functions...'; END $$;

-- Updated timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Payment validation function
CREATE OR REPLACE FUNCTION validate_payment_amount()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.payment_amount <= 0 THEN
        RAISE EXCEPTION 'Payment amount must be greater than 0';
    END IF;
    
    IF NEW.payment_amount > 1000000 THEN
        RAISE EXCEPTION 'Payment amount seems unreasonably high';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ⚡ TRIGGERS CREATION
-- =====================================================
DO $$ BEGIN RAISE NOTICE '🔧 Step 4/8: Creating triggers...'; END $$;

-- Products triggers
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Customers triggers
CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Customer debts triggers
CREATE TRIGGER update_customer_debts_updated_at
    BEFORE UPDATE ON customer_debts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Customer payments triggers
CREATE TRIGGER update_customer_payments_updated_at
    BEFORE UPDATE ON customer_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER validate_customer_payment
    BEFORE INSERT OR UPDATE ON customer_payments
    FOR EACH ROW
    EXECUTE FUNCTION validate_payment_amount();

-- =====================================================
-- 📊 VIEWS CREATION
-- =====================================================
DO $$ BEGIN RAISE NOTICE '🔧 Step 5/8: Creating views...'; END $$;

-- Customer balances view with comprehensive calculations
CREATE VIEW customer_balances AS
WITH debt_totals AS (
    SELECT
        customer_name,
        customer_family_name,
        SUM(product_price * quantity) as total_debt,
        COUNT(*) as debt_count,
        MAX(debt_date) as last_debt_date
    FROM customer_debts
    GROUP BY customer_name, customer_family_name
),
payment_totals AS (
    SELECT
        customer_name,
        customer_family_name,
        SUM(payment_amount) as total_payments,
        COUNT(*) as payment_count,
        MAX(payment_date) as last_payment_date
    FROM customer_payments
    GROUP BY customer_name, customer_family_name
)
SELECT
    COALESCE(d.customer_name, p.customer_name) as customer,
    COALESCE(d.customer_family_name, p.customer_family_name) as customer_family_name,
    COALESCE(d.total_debt, 0) as total_debt,
    COALESCE(p.total_payments, 0) as total_payment,
    COALESCE(d.total_debt, 0) - COALESCE(p.total_payments, 0) as remaining_balance,
    CASE
        WHEN COALESCE(d.total_debt, 0) - COALESCE(p.total_payments, 0) <= 0 THEN 'Paid'
        ELSE 'Outstanding'
    END as balance_status,
    CASE
        WHEN COALESCE(d.total_debt, 0) = 0 THEN 0
        ELSE ROUND((COALESCE(p.total_payments, 0) / d.total_debt * 100), 2)
    END as completion_percentage,
    COALESCE(d.debt_count, 0) as total_debts,
    COALESCE(p.payment_count, 0) as total_payments_count,
    d.last_debt_date,
    p.last_payment_date
FROM debt_totals d
FULL OUTER JOIN payment_totals p
    ON d.customer_name = p.customer_name
    AND d.customer_family_name = p.customer_family_name
ORDER BY remaining_balance DESC, customer;

-- =====================================================
-- 🔒 SECURITY POLICIES (RLS)
-- =====================================================
DO $$ BEGIN RAISE NOTICE '🔧 Step 6/8: Setting up security policies...'; END $$;

-- Enable RLS on all tables
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_payments ENABLE ROW LEVEL SECURITY;

-- Create policies for application access
CREATE POLICY "Enable all operations for application" ON products FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON customers FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON customer_debts FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON customer_payments FOR ALL USING (true);

-- =====================================================
-- 📈 PERFORMANCE INDEXES
-- =====================================================
DO $$ BEGIN RAISE NOTICE '🔧 Step 7/8: Creating performance indexes...'; END $$;

-- Products indexes
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);

-- Customers indexes
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone_number);

-- Customer debts indexes
CREATE INDEX IF NOT EXISTS idx_customer_debts_customer ON customer_debts(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_debts_date ON customer_debts(debt_date);
CREATE INDEX IF NOT EXISTS idx_customer_debts_product ON customer_debts(product_name);

-- Customer payments indexes
CREATE INDEX IF NOT EXISTS idx_customer_payments_customer ON customer_payments(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_payments_date ON customer_payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_customer_payments_amount ON customer_payments(payment_amount);

-- =====================================================
-- ✅ SETUP COMPLETION & VERIFICATION
-- =====================================================
DO $$ BEGIN RAISE NOTICE '🔧 Step 8/8: Finalizing setup...'; END $$;

-- Display completion message
DO $$
DECLARE
    products_count INTEGER;
    customers_count INTEGER;
    debts_count INTEGER;
    payments_count INTEGER;
BEGIN
    -- Count existing records
    SELECT COUNT(*) INTO products_count FROM products;
    SELECT COUNT(*) INTO customers_count FROM customers;
    SELECT COUNT(*) INTO debts_count FROM customer_debts;
    SELECT COUNT(*) INTO payments_count FROM customer_payments;

    RAISE NOTICE '';
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '🎉 TINDAHAN DATABASE SETUP COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '📊 CURRENT DATA SUMMARY:';
    RAISE NOTICE '   Products: % records', products_count;
    RAISE NOTICE '   Customers: % records', customers_count;
    RAISE NOTICE '   Debts: % records', debts_count;
    RAISE NOTICE '   Payments: % records', payments_count;
    RAISE NOTICE '';
    RAISE NOTICE '✅ All tables created/updated successfully';
    RAISE NOTICE '✅ All functions and triggers installed';
    RAISE NOTICE '✅ Security policies configured';
    RAISE NOTICE '✅ Performance indexes created';
    RAISE NOTICE '✅ Customer balance view ready';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your database is ready for production use!';
    RAISE NOTICE '📝 Test with: SELECT * FROM customer_balances;';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- 🧪 VERIFICATION QUERIES (OPTIONAL)
-- =====================================================
-- Uncomment these lines to test your setup:

-- SELECT 'Products table' as table_name, COUNT(*) as record_count FROM products
-- UNION ALL
-- SELECT 'Customers table', COUNT(*) FROM customers
-- UNION ALL
-- SELECT 'Customer debts table', COUNT(*) FROM customer_debts
-- UNION ALL
-- SELECT 'Customer payments table', COUNT(*) FROM customer_payments;

-- SELECT * FROM customer_balances LIMIT 5;

-- =====================================================
-- 🏁 END OF SINGLE FILE SETUP
-- =====================================================

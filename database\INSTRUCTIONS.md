# 🏪 TINDAHAN DATABASE - SINGLE FILE SETUP

## 🎯 **SIMPLE INSTRUCTIONS**

### **Para sa Production (GAMITIN ITO):**

1. **Open** Supabase SQL Editor
2. **Copy** ang BUONG content ng `tindahan_complete_single_file.sql`
3. **Paste** sa SQL Editor
4. **Run** (Ctrl+Enter)
5. **✅ TAPOS NA!**

---

## 📁 **Files Overview**

### ✅ **USE THIS FILE:**
- **`tindahan_complete_single_file.sql`** - Complete single file solution

### ❌ **IGNORE THESE FILES:**
- **`tindahan_unified_schema.sql`** - Old version (hindi na kailangan)

---

## 🛡️ **Safety Features**

- ✅ **Production-Safe** - Hindi mawawala ang existing data
- ✅ **Single File** - Isang copy-paste lang
- ✅ **Complete** - Lahat ng kailangan nandito na
- ✅ **Idempotent** - Safe i-run multiple times

---

## 🎉 **What You Get**

After running the single file:
- ✅ Products table
- ✅ Customers table  
- ✅ Customer debts table
- ✅ Customer payments table
- ✅ Customer balances view
- ✅ All functions and triggers
- ✅ Security policies
- ✅ Performance indexes

---

## 🧪 **Test Your Setup**

After running, test with:
```sql
SELECT * FROM customer_balances;
```

---

**🎯 REMEMBER: Use only `tindahan_complete_single_file.sql` - it's the complete solution!**

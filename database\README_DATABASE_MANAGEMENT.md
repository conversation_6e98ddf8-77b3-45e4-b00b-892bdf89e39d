# 🗄️ Database Management Guide

## 🚨 **IMPORTANT: Production Data Safety**

### ❌ **PROBLEMA NA NASOLVE**
Dati kapag nag-run kayo ng `tindahan_unified_schema.sql` sa Supabase SQL Editor, nawawala ang mga bagong data na naidagdag ninyo kasi may **DROP TABLE** commands.

### ✅ **SOLUTION**
Ginawa namin ang database management na **production-safe** - hindi na mawawala ang data ninyo!

---

## 📁 **Database Files Overview**

### 1. `tindahan_unified_schema.sql` *(UPDATED - Production Safe)*
- **Purpose**: Complete database setup for fresh installations
- **Safe for**: New projects, development environments
- **Changes Made**:
  - ✅ Removed `DROP TABLE` commands
  - ✅ Added `CREATE TABLE IF NOT EXISTS`
  - ✅ Added production safety comments
  - ✅ Preserves existing data

### 2. `production_safe_migration.sql` *(NEW)*
- **Purpose**: Safe updates for existing production databases
- **Safe for**: Production environments with existing data
- **Features**:
  - ✅ Can be run multiple times safely
  - ✅ Only updates functions, triggers, views, policies
  - ✅ Never touches table data
  - ✅ Checks if tables exist before operations

---

## 🔧 **How to Use**

### **For Production Updates (RECOMMENDED)**
```sql
-- Run this in Supabase SQL Editor for safe updates
-- File: production_safe_migration.sql
```

**Benefits:**
- ✅ Preserves all existing customer data
- ✅ Updates database structure safely
- ✅ Can run multiple times without issues
- ✅ Shows success messages

### **For Fresh Setup Only**
```sql
-- Only use for completely new databases
-- File: tindahan_unified_schema.sql
```

---

## 🛡️ **Production Safety Features**

### **1. Data Preservation**
- No `DROP TABLE` commands in production scripts
- Uses `CREATE TABLE IF NOT EXISTS`
- Existing data is never deleted

### **2. Idempotent Operations**
- Scripts can be run multiple times safely
- Checks for existing objects before creation
- Uses `IF EXISTS` and `IF NOT EXISTS` clauses

### **3. Smart Updates**
- Only updates functions, triggers, views, policies
- Preserves table structure and data
- Validates table existence before operations

---

## 📊 **Database Schema Overview**

### **Tables**
1. **`products`** - Product inventory management
2. **`customers`** - Customer profile information
3. **`customer_debts`** - Debt tracking records
4. **`customer_payments`** - Payment history

### **Views**
1. **`customer_balances`** - Comprehensive debt/payment summary

### **Functions**
1. **`update_updated_at_column()`** - Auto-update timestamps
2. **`validate_payment_amount()`** - Payment validation

### **Triggers**
- Auto-update `updated_at` columns
- Validate payment amounts
- Maintain data integrity

---

## 🚀 **Step-by-Step Instructions**

### **For Existing Production Database:**

1. **Open Supabase SQL Editor**
2. **Copy contents of `production_safe_migration.sql`**
3. **Paste and run (Ctrl+Enter)**
4. **✅ Your data is safe!**

### **For New Database Setup:**

1. **Open Supabase SQL Editor**
2. **Copy contents of `tindahan_unified_schema.sql`**
3. **Paste and run (Ctrl+Enter)**
4. **✅ Complete setup done!**

---

## 🔍 **Verification**

After running the migration, verify your data:

```sql
-- Check if your data is still there
SELECT COUNT(*) FROM customers;
SELECT COUNT(*) FROM customer_debts;
SELECT COUNT(*) FROM customer_payments;

-- View customer balances
SELECT * FROM customer_balances;
```

---

## 📝 **Best Practices**

### **DO ✅**
- Use `production_safe_migration.sql` for production updates
- Test changes in development first
- Backup your database before major changes
- Run migrations during low-traffic periods

### **DON'T ❌**
- Don't use full schema file on production with existing data
- Don't run `DROP TABLE` commands on production
- Don't skip testing migrations
- Don't forget to verify data after migrations

---

## 🆘 **Troubleshooting**

### **If Data Seems Missing:**
1. Check if you ran the wrong script
2. Verify table names are correct
3. Check for any error messages in SQL Editor
4. Contact support if data recovery is needed

### **If Migration Fails:**
1. Read error messages carefully
2. Check if tables exist: `\dt` in SQL Editor
3. Verify permissions
4. Try running sections individually

---

## 📞 **Support**

If you encounter any issues:
1. Check error messages in Supabase SQL Editor
2. Verify your database permissions
3. Test with a small dataset first
4. Document any errors for troubleshooting

---

**🎉 Congratulations! Your database is now production-safe!**

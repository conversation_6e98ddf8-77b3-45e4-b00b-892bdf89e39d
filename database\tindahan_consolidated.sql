-- =====================================================
-- TINDAHAN CONSOLIDATED DATABASE SCRIPT
-- =====================================================
-- PRODUCTION-SAFE: This script can be safely run multiple times
-- without losing existing data. All tables use IF NOT EXISTS.
-- 
-- INSTRUCTIONS:
-- 1. Copy this entire file
-- 2. Paste into Supabase SQL Editor
-- 3. Run the script (Ctrl+Enter)
-- 4. Your data will be preserved!
--
-- AUTHOR: Revantad Store Management System
-- VERSION: 2.0 (Production-Safe)
-- =====================================================

-- Enable UUID extension if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- CORE FUNCTIONS
-- =====================================================

-- Drop and recreate functions (safe operation)
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS validate_payment_amount() CASCADE;

-- Updated timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Payment validation function
CREATE OR REPLACE FUNCTION validate_payment_amount()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate payment amount is positive
    IF NEW.payment_amount <= 0 THEN
        RAISE EXCEPTION 'Payment amount must be greater than 0';
    END IF;
    
    -- Validate payment amount is reasonable (not more than 1 million)
    IF NEW.payment_amount > 1000000 THEN
        RAISE EXCEPTION 'Payment amount seems unreasonably high';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CORE TABLES DEFINITION
-- =====================================================

-- PRODUCTS TABLE - Inventory management with comprehensive validation
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    image_url TEXT,
    net_weight VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    category VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Data integrity constraints
    CONSTRAINT products_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT products_category_not_empty CHECK (LENGTH(TRIM(category)) > 0),
    CONSTRAINT products_net_weight_not_empty CHECK (LENGTH(TRIM(net_weight)) > 0)
);

-- CUSTOMERS TABLE - Profile management with Cloudinary support
CREATE TABLE IF NOT EXISTS customers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    profile_picture_url TEXT,
    profile_picture_public_id TEXT, -- Cloudinary public ID
    phone_number VARCHAR(20),
    address TEXT,
    birth_date DATE, -- Customer's birth date
    birth_place VARCHAR(255), -- Customer's birthplace
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Data integrity constraints
    CONSTRAINT customers_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customers_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customers_birth_date_valid CHECK (birth_date IS NULL OR birth_date <= CURRENT_DATE),
    CONSTRAINT customers_birth_place_not_empty CHECK (birth_place IS NULL OR LENGTH(TRIM(birth_place)) > 0)
);

-- CUSTOMER DEBTS TABLE - Debt tracking with automatic calculations
CREATE TABLE IF NOT EXISTS customer_debts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL CHECK (product_price > 0),
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Data integrity constraints
    CONSTRAINT customer_debts_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customer_debts_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customer_debts_product_name_not_empty CHECK (LENGTH(TRIM(product_name)) > 0),
    CONSTRAINT customer_debts_reasonable_quantity CHECK (quantity <= 1000),
    CONSTRAINT customer_debts_valid_date CHECK (debt_date >= '2020-01-01' AND debt_date <= CURRENT_DATE + INTERVAL '1 day')
);

-- CUSTOMER PAYMENTS TABLE - Payment tracking with family member responsibility
CREATE TABLE IF NOT EXISTS customer_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    payment_amount DECIMAL(10,2) NOT NULL CHECK (payment_amount > 0),
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'Cash',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Data integrity constraints
    CONSTRAINT customer_payments_name_not_empty CHECK (LENGTH(TRIM(customer_name)) > 0),
    CONSTRAINT customer_payments_family_name_not_empty CHECK (LENGTH(TRIM(customer_family_name)) > 0),
    CONSTRAINT customer_payments_method_not_empty CHECK (LENGTH(TRIM(payment_method)) > 0),
    CONSTRAINT customer_payments_valid_date CHECK (payment_date >= '2020-01-01' AND payment_date <= CURRENT_DATE + INTERVAL '1 day')
);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Drop existing triggers
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
DROP TRIGGER IF EXISTS update_customer_debts_updated_at ON customer_debts;
DROP TRIGGER IF EXISTS update_customer_payments_updated_at ON customer_payments;
DROP TRIGGER IF EXISTS validate_customer_payment ON customer_payments;

-- Recreate triggers (only if tables exist)
DO $$
BEGIN
    -- Products table triggers
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        CREATE TRIGGER update_products_updated_at
            BEFORE UPDATE ON products
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Customers table triggers
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customers') THEN
        CREATE TRIGGER update_customers_updated_at
            BEFORE UPDATE ON customers
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Customer debts table triggers
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customer_debts') THEN
        CREATE TRIGGER update_customer_debts_updated_at
            BEFORE UPDATE ON customer_debts
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Customer payments table triggers
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customer_payments') THEN
        CREATE TRIGGER update_customer_payments_updated_at
            BEFORE UPDATE ON customer_payments
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
            
        CREATE TRIGGER validate_customer_payment
            BEFORE INSERT OR UPDATE ON customer_payments
            FOR EACH ROW
            EXECUTE FUNCTION validate_payment_amount();
    END IF;
END $$;

-- =====================================================
-- VIEWS
-- =====================================================

-- Drop and recreate view (safe operation)
DROP VIEW IF EXISTS customer_balances CASCADE;

-- Recreate customer balances view (only if tables exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customer_debts') 
       AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customer_payments') THEN
        
        CREATE VIEW customer_balances AS
        WITH debt_totals AS (
            SELECT 
                customer_name,
                customer_family_name,
                SUM(product_price * quantity) as total_debt,
                COUNT(*) as debt_count,
                MAX(debt_date) as last_debt_date
            FROM customer_debts
            GROUP BY customer_name, customer_family_name
        ),
        payment_totals AS (
            SELECT 
                customer_name,
                customer_family_name,
                SUM(payment_amount) as total_payments,
                COUNT(*) as payment_count,
                MAX(payment_date) as last_payment_date
            FROM customer_payments
            GROUP BY customer_name, customer_family_name
        )
        SELECT 
            COALESCE(d.customer_name, p.customer_name) as customer,
            COALESCE(d.customer_family_name, p.customer_family_name) as customer_family_name,
            COALESCE(d.total_debt, 0) as total_debt,
            COALESCE(p.total_payments, 0) as total_payment,
            COALESCE(d.total_debt, 0) - COALESCE(p.total_payments, 0) as remaining_balance,
            CASE 
                WHEN COALESCE(d.total_debt, 0) - COALESCE(p.total_payments, 0) <= 0 THEN 'Paid'
                ELSE 'Outstanding'
            END as balance_status,
            CASE 
                WHEN COALESCE(d.total_debt, 0) = 0 THEN 0
                ELSE ROUND((COALESCE(p.total_payments, 0) / d.total_debt * 100), 2)
            END as completion_percentage,
            COALESCE(d.debt_count, 0) as total_debts,
            COALESCE(p.payment_count, 0) as total_payments_count,
            d.last_debt_date,
            p.last_payment_date
        FROM debt_totals d
        FULL OUTER JOIN payment_totals p 
            ON d.customer_name = p.customer_name 
            AND d.customer_family_name = p.customer_family_name
        ORDER BY remaining_balance DESC, customer;
    END IF;
END $$;

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON products;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customers;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customer_debts;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON customer_payments;
DROP POLICY IF EXISTS "Enable all operations for application" ON products;
DROP POLICY IF EXISTS "Enable all operations for application" ON customers;
DROP POLICY IF EXISTS "Enable all operations for application" ON customer_debts;
DROP POLICY IF EXISTS "Enable all operations for application" ON customer_payments;

-- Recreate policies (only if tables exist)
DO $$
BEGIN
    -- Products table policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        ALTER TABLE products ENABLE ROW LEVEL SECURITY;
        CREATE POLICY "Enable all operations for application" ON products FOR ALL USING (true);
    END IF;
    
    -- Customers table policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customers') THEN
        ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
        CREATE POLICY "Enable all operations for application" ON customers FOR ALL USING (true);
    END IF;
    
    -- Customer debts table policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customer_debts') THEN
        ALTER TABLE customer_debts ENABLE ROW LEVEL SECURITY;
        CREATE POLICY "Enable all operations for application" ON customer_debts FOR ALL USING (true);
    END IF;
    
    -- Customer payments table policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customer_payments') THEN
        ALTER TABLE customer_payments ENABLE ROW LEVEL SECURITY;
        CREATE POLICY "Enable all operations for application" ON customer_payments FOR ALL USING (true);
    END IF;
END $$;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Tindahan database setup completed successfully!';
    RAISE NOTICE '📊 All existing data has been preserved.';
    RAISE NOTICE '🔄 Database structure has been updated safely.';
END $$;
